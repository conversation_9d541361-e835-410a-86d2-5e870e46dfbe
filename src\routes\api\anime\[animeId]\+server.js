import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET({ params }) {
  try {
    const [episodesResponse, metadataResponse, rankingsResponse, relatedResponse] = await Promise.all([
      supabase
        .from('anime_new')
        .select('*')
        .eq('anilist_id', params.animeId),
      supabase
        .from('anime_metadata')
        .select('*')
        .eq('anilist_id', params.animeId)
        .single(),
      supabase
        .from('anime_rankings')
        .select('*')
        .eq('anilist_id', params.animeId)
        .single(),
      supabase
        .from('anime_relations')
        .select(`
          relation_type,
          target:target_id(
            anilist_id,
            romaji_title,
            cover_image,
            format
          )
        `)
        .eq('source_id', params.animeId)
    ]);

    if (episodesResponse.error) throw episodesResponse.error;
    if (metadataResponse.error) throw metadataResponse.error;
    if (rankingsResponse.error) throw rankingsResponse.error;
    if (relatedResponse.error) throw relatedResponse.error;

    if (!metadataResponse.data) {
      throw error(404, 'Anime not found');
    }

    if (metadataResponse.data && metadataResponse.data.hidden) {
      throw error(404, 'Anime not found');
    }

    const episodes = episodesResponse.data;
    const meta = metadataResponse.data;
    const ranking = rankingsResponse.data;

    const relatedAnime = relatedResponse.data
      ?.filter(relation => relation.target)
      ?.map(relation => ({
        id: relation.target.anilist_id,
        title: relation.target.romaji_title,
        poster: relation.target.cover_image,
        relation: relation.relation_type,
        type: relation.target.format
      })) || [];

    const now = new Date();
    const releasedEpisodes = episodes.filter(ep =>
      new Date(ep.date_added) <= now
    ).length;

    const getRanking = (type, format = null, timespan = 'all') => {
      const rankings = ranking?.rankings || [];
      let filtered = rankings.filter(r => r.type === type);

      if (format) {
        filtered = filtered.filter(r => r.format === format);
      }

      if (timespan === 'all') {
        filtered = filtered.filter(r => r.allTime);
      } else if (timespan === 'current') {
        filtered = filtered.filter(r => {
          const currentYear = new Date().getFullYear();
          return !r.allTime && r.year === currentYear;
        });
      }

      if (filtered.length === 0) return null;

      return {
        id: filtered[0].id,
        rank: filtered[0].rank,
        type: filtered[0].type,
        format: filtered[0].format,
        year: filtered[0].year,
        season: filtered[0].season,
        allTime: filtered[0].allTime,
        context: filtered[0].context
      };
    };

    // Group episodes by episode number to handle multiple groups per episode
    const episodeGroups = {};
    episodes.forEach(ep => {
      const episodeNumber = ep.episode_number;
      if (!episodeGroups[episodeNumber]) {
        episodeGroups[episodeNumber] = [];
      }
      episodeGroups[episodeNumber].push(ep);
    });

    // Create episodes array with all groups for each episode
    const processedEpisodes = Object.keys(episodeGroups)
      .map(Number)
      .sort((a, b) => a - b)
      .map(episodeNumber => {
        const episodeEntries = episodeGroups[episodeNumber];

        // Find the best base episode - prioritize lycoris_cafe entry if it exists, otherwise use first entry
        let baseEpisode = episodeEntries[0];
        const lycorisEntry = episodeEntries.find(entry =>
          entry.translating_group === 'lycoris_cafe' ||
          (entry.secondary_source && (typeof entry.secondary_source === 'string' ? entry.secondary_source.trim() : Object.keys(entry.secondary_source).length > 0))
        );

        if (lycorisEntry) {
          baseEpisode = lycorisEntry;
          console.log('Using lycoris.cafe entry as base episode:', baseEpisode.id);
        } else {
          console.log('Using first entry as base episode:', baseEpisode.id);
        }

        // Parse baseEpisode secondary_source if it's a string
        let parsedBaseSecondarySource = baseEpisode.secondary_source;
        if (typeof baseEpisode.secondary_source === 'string' && baseEpisode.secondary_source.trim()) {
          try {
            parsedBaseSecondarySource = JSON.parse(baseEpisode.secondary_source);
            console.log('Parsed baseEpisode secondary_source from string:', parsedBaseSecondarySource);
          } catch (e) {
            console.error('Failed to parse baseEpisode secondary_source JSON:', e);
            parsedBaseSecondarySource = null;
          }
        }

        // Create allGroups object with all available players for this episode
        const allGroups = {};

        episodeEntries.forEach(entry => {
          console.log('=== EPISODE ENTRY DEBUG ===');
          console.log('Entry ID:', entry.id);
          console.log('Entry secondary_source raw:', entry.secondary_source);
          console.log('Entry secondary_source type:', typeof entry.secondary_source);

          // Parse secondary_source if it's a string
          let parsedSecondarySource = entry.secondary_source;
          if (typeof entry.secondary_source === 'string' && entry.secondary_source.trim()) {
            try {
              parsedSecondarySource = JSON.parse(entry.secondary_source);
              console.log('Parsed secondary_source from string:', parsedSecondarySource);
            } catch (e) {
              console.error('Failed to parse secondary_source JSON:', e);
              parsedSecondarySource = null;
            }
          }

          console.log('Entry secondary_source parsed:', parsedSecondarySource);
          console.log('=== END EPISODE ENTRY DEBUG ===');

          // Add lycoris.cafe if secondary source exists
          if (parsedSecondarySource && typeof parsedSecondarySource === 'object' && Object.keys(parsedSecondarySource).length > 0) {
            const lycorisUrl = parsedSecondarySource.FHD || parsedSecondarySource.HD || parsedSecondarySource.SD;
            console.log('Found lycoris.cafe source:', lycorisUrl);
            if (lycorisUrl) {
              allGroups['lycoris.cafe'] = {
                players: [{ 'lycoris.cafe': lycorisUrl }],
                logo_url: 'https://lycoris.cafe/logo.png',
                quality: 'HD',
                audio_language: entry.audio_language || 'jp',
                subtitle_language: entry.subtitle_language || 'pl'
              };
              console.log('Added lycoris.cafe to allGroups');
            }
          } else {
            console.log('No valid secondary_source found for entry:', entry.id);
          }

          // Add other groups if they exist
          if (entry.translating_group && entry.translating_group !== 'lycoris_cafe' && entry.external_player_link) {
            if (!allGroups[entry.translating_group]) {
              allGroups[entry.translating_group] = {
                players: [],
                logo_url: 'https://lycoris.cafe/logo.png', // Default logo, can be updated later
                quality: entry.quality || 'HD',
                audio_language: entry.audio_language || 'jp',
                subtitle_language: entry.subtitle_language || 'pl'
              };
            }

            // Add the player to the group's players array
            allGroups[entry.translating_group].players.push({
              [entry.player_source || 'external']: entry.external_player_link,
              quality: entry.quality || 'HD',
              audio_language: entry.audio_language || 'jp',
              subtitle_language: entry.subtitle_language || 'pl',
              date_added: entry.date_added
            });

            // Update group's date_added to the earliest date (first player added)
            if (!allGroups[entry.translating_group].date_added ||
                (entry.date_added && new Date(entry.date_added) < new Date(allGroups[entry.translating_group].date_added))) {
              allGroups[entry.translating_group].date_added = entry.date_added;
            }
          }
        });

        return {
          id: baseEpisode.id,
          mal_id: baseEpisode.mal_id,
          markerPeriods: baseEpisode.markerPeriods,
          anilist_id: baseEpisode.anilist_id,
          number: episodeNumber,
          title: baseEpisode.episode_title || `Episode ${episodeNumber}`,
          thumbnail: baseEpisode.thumbnail_link,
          preview: baseEpisode.preview_file,
          burstSource: baseEpisode.burst_source,
          primarySource: baseEpisode.primary_source,
          secondarySource: parsedBaseSecondarySource, // Use parsed version
          subtitles: baseEpisode.subtitleLinks,
          progress: 0,
          watched: false,
          airDate: baseEpisode.date_added,
          translating_group: baseEpisode.translating_group,
          player_source: baseEpisode.player_source,
          external_player_link: baseEpisode.external_player_link,
          quality: baseEpisode.quality,
          audio_language: baseEpisode.audio_language,
          subtitle_language: baseEpisode.subtitle_language,
          allGroups: allGroups // New field with all available groups/players
        };
      });

    const anime = {
      id: params.animeId,
      mal_id: meta.mal_id,
      title: meta.romaji_title,
      englishTitle: meta.english_title,
      nativeTitle: meta.native_title,
      poster: meta.cover_image,
      background: meta.banner_image,
      synopsis: meta.description,
      shortSynopsis: meta.description?.slice(0, 250) + (meta.description?.length >= 250 ? '...' : ''),
      rating: ranking?.average_score / 10,
      popularity: ranking?.popularity,
      trending: ranking?.trending,
      favourites: ranking?.favourites,
      rankings: {
        overall: getRanking('RATED', null, 'all'),
        current: getRanking('RATED', null, 'current'),
        popular: {
          all: getRanking('POPULAR', null, 'all'),
          current: getRanking('POPULAR', null, 'current'),
        },
        rated: {
          all: getRanking('RATED', null, 'all'),
          current: getRanking('RATED', null, 'current'),
        },
        format: {
          all: getRanking('RATED', meta.format, 'all'),
          current: getRanking('RATED', meta.format, 'current'),
        },
        raw: ranking?.rankings || []
      },
      format: meta.format,
      studio: meta.studios?.[0],
      source: meta.source,
      season: meta.season,
      seasonYear: meta.season_year,
      genres: meta.genres,
      totalEpisodes: meta.episodes,
      duration: meta.duration,
      delayedAiring: meta.delayed_airing,
      episodes: processedEpisodes,
      releasedEpisodes,
      airingStatus: ranking?.status,
      nextAiringEpisode: ranking?.airing_schedule ? {
        id: ranking.airing_schedule.id,
        episode: ranking.airing_schedule.episode,
        airingAt: (ranking.airing_schedule.airingAt * 1000) +
          (ranking.airing_schedule_offset || 0) * 60 * 60 * 1000
      } : null,
      startDate: meta.start_date,
      endDate: meta.end_date,
      lastUpdated: meta.last_updated,
      relatedEntries: relatedAnime
    };

    return json({ anime });

  } catch (e) {
    console.error('Error fetching anime:', e);

    // If it's already a 404 error thrown by our code, pass it through
    if (e.status === 404) {
      return new Response(JSON.stringify({ error: e.body?.message || 'Anime not found' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Handle database not found errors (PGRST116)
    if (e.code === 'PGRST116' || e.message?.includes('not found')) {
      return new Response(JSON.stringify({ error: 'Anime not found' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // For all other errors, return 500
    return new Response(JSON.stringify({ error: 'Failed to load anime data' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}