import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET({ params, url }) {
  try {
    // Get query parameters
    const translatingGroup = url.searchParams.get('translatingGroup');
    
    console.log('=== EPISODE API REQUEST DEBUG ===');
    console.log('Anime ID:', params.animeId);
    console.log('Episode Number:', params.episodeNumber);
    console.log('Requested translating group:', translatingGroup);
    console.log('=== END EPISODE API REQUEST DEBUG ===');

    // Fetch all episode entries for this anime and episode number
    const { data: episodeEntries, error: episodeError } = await supabase
      .from('anime_new')
      .select('*')
      .eq('anilist_id', params.animeId)
      .eq('episode_number', parseInt(params.episodeNumber));

    if (episodeError) {
      console.error('Database error:', episodeError);
      throw error(500, 'Failed to fetch episode data');
    }

    if (!episodeEntries || episodeEntries.length === 0) {
      throw error(404, 'Episode not found');
    }

    console.log('Found episode entries:', episodeEntries.length);

    // Select the correct episode entry based on translating group
    let selectedEntry = episodeEntries[0]; // Default fallback

    if (translatingGroup) {
      const requestedEntry = episodeEntries.find(entry => {
        if (translatingGroup === 'lycoris.cafe') {
          return entry.translating_group === 'lycoris_cafe' || 
                 (entry.secondary_source && (
                   typeof entry.secondary_source === 'string' ? 
                   entry.secondary_source.trim() : 
                   Object.keys(entry.secondary_source).length > 0
                 ));
        } else {
          return entry.translating_group === translatingGroup;
        }
      });

      if (requestedEntry) {
        selectedEntry = requestedEntry;
        console.log(`Using ${translatingGroup} entry:`, selectedEntry.id);
      } else {
        console.log(`Requested translating group ${translatingGroup} not found, using first entry:`, selectedEntry.id);
      }
    } else {
      console.log('No translating group specified, using first entry:', selectedEntry.id);
    }

    // Parse secondary_source if it's a string
    let parsedSecondarySource = selectedEntry.secondary_source;
    if (typeof selectedEntry.secondary_source === 'string' && selectedEntry.secondary_source.trim()) {
      try {
        parsedSecondarySource = JSON.parse(selectedEntry.secondary_source);
        console.log('Parsed secondary_source from string:', parsedSecondarySource);
      } catch (e) {
        console.error('Failed to parse secondary_source JSON:', e);
        parsedSecondarySource = null;
      }
    }

    // Create allGroups object with all available players for this episode
    const allGroups = {};

    episodeEntries.forEach(entry => {
      console.log('=== EPISODE ENTRY DEBUG ===');
      console.log('Entry ID:', entry.id);
      console.log('Entry translating_group:', entry.translating_group);
      console.log('Entry secondary_source raw:', entry.secondary_source);
      
      // Parse secondary_source if it's a string
      let entryParsedSecondarySource = entry.secondary_source;
      if (typeof entry.secondary_source === 'string' && entry.secondary_source.trim()) {
        try {
          entryParsedSecondarySource = JSON.parse(entry.secondary_source);
          console.log('Parsed entry secondary_source from string:', entryParsedSecondarySource);
        } catch (e) {
          console.error('Failed to parse entry secondary_source JSON:', e);
          entryParsedSecondarySource = null;
        }
      }
      
      console.log('Entry secondary_source parsed:', entryParsedSecondarySource);
      console.log('=== END EPISODE ENTRY DEBUG ===');
      
      // Add lycoris.cafe if secondary source exists
      if (entryParsedSecondarySource && typeof entryParsedSecondarySource === 'object' && Object.keys(entryParsedSecondarySource).length > 0) {
        const lycorisUrl = entryParsedSecondarySource.FHD || entryParsedSecondarySource.HD || entryParsedSecondarySource.SD;
        console.log('Found lycoris.cafe source:', lycorisUrl);
        if (lycorisUrl) {
          allGroups['lycoris.cafe'] = {
            players: [{ 'lycoris.cafe': lycorisUrl }],
            logo_url: 'https://lycoris.cafe/logo.png',
            quality: 'HD',
            audio_language: entry.audio_language || 'jp',
            subtitle_language: entry.subtitle_language || 'pl'
          };
          console.log('Added lycoris.cafe to allGroups');
        }
      } else {
        console.log('No valid secondary_source found for entry:', entry.id);
      }

      // Add other groups if they exist
      if (entry.translating_group && entry.translating_group !== 'lycoris_cafe' && entry.external_player_link) {
        if (!allGroups[entry.translating_group]) {
          allGroups[entry.translating_group] = {
            players: [],
            logo_url: 'https://lycoris.cafe/logo.png', // Default logo
            quality: entry.quality || 'HD',
            audio_language: entry.audio_language || 'jp',
            subtitle_language: entry.subtitle_language || 'pl'
          };
        }

        // Add player to the group
        allGroups[entry.translating_group].players.push({
          [entry.player_source || 'external']: entry.external_player_link,
          quality: entry.quality || 'HD',
          audio_language: entry.audio_language || 'jp',
          subtitle_language: entry.subtitle_language || 'pl',
          date_added: entry.date_added
        });
      }
    });

    // Return the episode data with the selected entry as base
    const episodeData = {
      id: selectedEntry.id,
      mal_id: selectedEntry.mal_id,
      markerPeriods: selectedEntry.markerPeriods,
      anilist_id: selectedEntry.anilist_id,
      number: parseInt(params.episodeNumber),
      title: selectedEntry.episode_title || `Episode ${params.episodeNumber}`,
      thumbnail: selectedEntry.thumbnail_link,
      preview: selectedEntry.preview_file,
      burstSource: selectedEntry.burst_source,
      primarySource: selectedEntry.primary_source,
      secondarySource: parsedSecondarySource, // Use parsed version
      subtitles: selectedEntry.subtitleLinks,
      progress: 0,
      watched: false,
      airDate: selectedEntry.date_added,
      translating_group: selectedEntry.translating_group,
      player_source: selectedEntry.player_source,
      external_player_link: selectedEntry.external_player_link,
      quality: selectedEntry.quality,
      audio_language: selectedEntry.audio_language,
      subtitle_language: selectedEntry.subtitle_language,
      allGroups: allGroups // All available groups/players for this episode
    };

    console.log('Returning episode data:', episodeData);

    return json({
      episode: episodeData,
      success: true
    });

  } catch (err) {
    console.error('Error in episode API:', err);
    
    // If it's already a SvelteKit error, pass it through
    if (err.status) {
      throw err;
    }
    
    // For all other errors, return 500
    throw error(500, 'Failed to fetch episode data');
  }
}
